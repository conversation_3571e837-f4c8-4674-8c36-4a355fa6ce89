import os
import fitz  # PyMuPDF
import re

# 🔷 Шлях до папки з PDF-файлами (буде запитано у користувача)
folder_path = None

# 🔄 Список замін для української мови
ukrainian_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Фахівець з геодезії та землеустрою': '',
    'Основна	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': 'Мова(и) навчання/оцінювання',
}

# 🔄 Список замін для англійської мови
english_replacements = {
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Specialist in geodesy and land management': '',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Name and status of institution (if different from 2.3) administering studies': 'Language(s) of instruction/examination',
}

# Текст для видалення
text_to_remove = [
    'Зазначено у пункті 2.3\tSpecified in 2.3',
    '2.5\tМова(и) навчання/оцінювання\t2.5 Language(s) of instruction/examination'
]

def is_ukrainian_text(text):
    """Визначає, чи містить текст українські символи"""
    ukrainian_chars = 'абвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ'
    return any(char in ukrainian_chars for char in text)

def is_english_text(text):
    """Визначає, чи містить текст англійські символи"""
    return any(char.isalpha() and char.isascii() for char in text)

def get_appropriate_replacements(text):
    """Повертає відповідний словник замін залежно від мови тексту"""
    if is_ukrainian_text(text):
        return ukrainian_replacements
    elif is_english_text(text):
        return english_replacements
    else:
        # Якщо мова не визначена, використовуємо обидва словники
        return {**ukrainian_replacements, **english_replacements}

def replace_text_in_pdf(file_path):
    """Основна функція для заміни тексту в PDF документі"""
    try:
        # Відкриваємо PDF документ
        doc = fitz.open(file_path)
        total_changes = 0

        print(f"📄 Обробка PDF документа: {file_path}")

        # Обробляємо кожну сторінку
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"  📄 Сторінка {page_num + 1}")

            # Отримуємо весь текст зі сторінки
            page_text = page.get_text()

            # Видалення вказаних рядків
            for remove_text in text_to_remove:
                if remove_text in page_text:
                    print(f"    🗑️  Видалення: '{remove_text}'")
                    # Знаходимо та видаляємо текст
                    text_instances = page.search_for(remove_text)
                    for inst in text_instances:
                        page.add_redact_annot(inst, "")
                    total_changes += len(text_instances)

            # Застосовуємо заміни
            all_replacements = {**ukrainian_replacements, **english_replacements}

            for old_text, new_text in all_replacements.items():
                if old_text in page_text:
                    print(f"    🔄 Заміна: '{old_text[:50]}...' → '{new_text[:50]}...'")

                    # Знаходимо всі входження тексту
                    text_instances = page.search_for(old_text)

                    for inst in text_instances:
                        # Видаляємо старий текст
                        page.add_redact_annot(inst, "")

                        # Додаємо новий текст в тому ж місці
                        if new_text:  # Тільки якщо новий текст не порожній
                            # Отримуємо шрифт та розмір з оригінального тексту
                            try:
                                # Вставляємо новий текст
                                page.insert_text(
                                    inst.tl,  # Позиція (top-left)
                                    new_text,
                                    fontsize=10,  # Розмір шрифту
                                    color=(0, 0, 0)  # Чорний колір
                                )
                            except Exception as e:
                                print(f"      ⚠️  Помилка при вставці тексту: {e}")

                    total_changes += len(text_instances)

            # Застосовуємо всі зміни на сторінці
            page.apply_redactions()

        # Зберігаємо змінений документ
        output_path = file_path.replace('.pdf', '_modified.pdf')
        doc.save(output_path)
        doc.close()

        print(f"  ✅ Збережено як: {output_path}")
        return total_changes

    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def convert_docx_to_pdf(docx_path):
    """Конвертує DOCX файл в PDF"""
    try:
        import win32com.client

        # Створюємо об'єкт Word
        word = win32com.client.Dispatch("Word.Application")
        word.Visible = False

        # Відкриваємо документ
        doc = word.Documents.Open(docx_path)

        # Зберігаємо як PDF
        pdf_path = docx_path.replace('.docx', '.pdf')
        doc.SaveAs2(pdf_path, FileFormat=17)  # 17 = PDF format

        # Закриваємо документ та Word
        doc.Close()
        word.Quit()

        print(f"  📄 Конвертовано: {os.path.basename(docx_path)} → {os.path.basename(pdf_path)}")
        return pdf_path

    except Exception as e:
        print(f"  ❌ Помилка конвертації {docx_path}: {str(e)}")
        return None

def get_folder_path():
    """Запитує у користувача шлях до папки з файлами"""
    print("🔧 Скрипт заміни тексту в PDF документах")
    print("=" * 60)

    while True:
        folder_path = input("📁 Введіть шлях до папки з файлами: ").strip()

        # Видаляємо лапки, якщо користувач їх додав
        if folder_path.startswith('"') and folder_path.endswith('"'):
            folder_path = folder_path[1:-1]
        if folder_path.startswith("'") and folder_path.endswith("'"):
            folder_path = folder_path[1:-1]

        if not folder_path:
            print("❌ Шлях не може бути порожнім!")
            continue

        if not os.path.exists(folder_path):
            print(f"❌ Папка не знайдена: {folder_path}")
            retry = input("Спробувати ще раз? (y/n): ").lower()
            if retry != 'y':
                return None
            continue

        if not os.path.isdir(folder_path):
            print(f"❌ Це не папка: {folder_path}")
            continue

        return folder_path

def main():
    """Головна функція для обробки всіх файлів у папці"""
    folder_path = get_folder_path()

    if not folder_path:
        print("❌ Операція скасована")
        return

    # Перевіряємо, чи є PDF файли
    pdf_files = [f for f in os.listdir(folder_path) if f.endswith('.pdf')]
    docx_files = [f for f in os.listdir(folder_path) if f.endswith('.docx') and not f.startswith('~$')]

    print(f"📁 Папка для обробки: {folder_path}")
    print(f"📄 Знайдено PDF файлів: {len(pdf_files)}")
    print(f"📄 Знайдено DOCX файлів: {len(docx_files)}")

    # Пропонуємо конвертувати DOCX в PDF
    if docx_files and not pdf_files:
        convert = input("\n🔄 Конвертувати DOCX файли в PDF? (y/n): ").lower() == 'y'
        if convert:
            print("\n🔄 Конвертація DOCX в PDF...")
            for filename in docx_files:
                docx_path = os.path.join(folder_path, filename)
                pdf_path = convert_docx_to_pdf(docx_path)
                if pdf_path:
                    pdf_files.append(os.path.basename(pdf_path))

    if not pdf_files:
        print("❌ Не знайдено PDF файлів для обробки")
        return

    processed_files = 0
    total_changes = 0

    print(f"\n🔄 Початок обробки PDF файлів...")
    print("-" * 50)

    for filename in pdf_files:
        if filename.endswith('.pdf'):
            full_path = os.path.join(folder_path, filename)
            print(f'\n🔄 Обробка файлу: {filename}')

            changes = replace_text_in_pdf(full_path)
            if changes > 0:
                print(f'✅ Заміна виконана у файлі: {filename} ({changes} змін)')
                total_changes += changes
            else:
                print(f'ℹ️  Змін не знайдено у файлі: {filename}')

            processed_files += 1

    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print("=" * 60)

if __name__ == "__main__":
    main()
