import os
from docx import Document

# 🔷 Шлях до папки з Word-файлами (поточна папка)
folder_path = "."

# 🔄 Список замін для української мови
ukrainian_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Фахівець з геодезії та землеустрою': '',
    '<PERSON><PERSON><PERSON><PERSON>на	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': 'Мова(и) навчання/оцінювання',
}

# 🔄 Список замін для англійської мови
english_replacements = {
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and  educational-professional degree conferred (in original language)',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Specialist in geodesy and land management': '',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Name and status of institution (if different from 2.3) administering studies': 'Language(s) of instruction/examination',
}

# Текст для видалення
text_to_remove = [
    'Зазначено у пункті 2.3\tSpecified in 2.3',
    '2.5\tМова(и) навчання/оцінювання\t2.5 Language(s) of instruction/examination'
]

def is_ukrainian_text(text):
    """Визначає, чи містить текст українські символи"""
    ukrainian_chars = 'абвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ'
    return any(char in ukrainian_chars for char in text)

def is_english_text(text):
    """Визначає, чи містить текст англійські символи"""
    return any(char.isalpha() and char.isascii() for char in text)

def replace_text_in_run_preserving_format(run, old_text, new_text, verbose=False):
    """Замінює текст у run зі збереженням форматування"""
    if old_text in run.text:
        if verbose:
            print(f"    🔄 Заміна: '{old_text}' → '{new_text}'")
        
        # Зберігаємо форматування
        font = run.font
        original_bold = font.bold
        original_italic = font.italic
        original_underline = font.underline
        original_size = font.size
        original_color = font.color.rgb if font.color.rgb else None
        
        # Замінюємо текст
        run.text = run.text.replace(old_text, new_text)
        
        # Відновлюємо форматування
        font.bold = original_bold
        font.italic = original_italic
        font.underline = original_underline
        if original_size:
            font.size = original_size
        if original_color:
            font.color.rgb = original_color
        
        return True
    return False

def replace_text_in_paragraph(paragraph, verbose=False):
    """Замінює текст у параграфі зі збереженням форматування"""
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False

    # Видалення вказаних рядків
    for remove_text in text_to_remove:
        if remove_text in full_text:
            if verbose:
                print(f"    🗑️  Видалення: '{remove_text}'")
            for run in paragraph.runs:
                if remove_text in run.text:
                    run.text = run.text.replace(remove_text, '')
                    changed = True

    # Визначаємо мову тексту та застосовуємо відповідні заміни
    if is_ukrainian_text(full_text):
        replacements = ukrainian_replacements
        if verbose and full_text.strip():
            print(f"    🇺🇦 Українська мова: {full_text[:50]}...")
    elif is_english_text(full_text):
        replacements = english_replacements
        if verbose and full_text.strip():
            print(f"    🇺🇸 Англійська мова: {full_text[:50]}...")
    else:
        # Якщо мова не визначена, використовуємо обидва словники
        replacements = {**ukrainian_replacements, **english_replacements}

    # Застосування замін
    for old, new in replacements.items():
        for run in paragraph.runs:
            if replace_text_in_run_preserving_format(run, old, new, verbose):
                changed = True

    return changed

def replace_text_in_cell(cell, verbose=False):
    """Замінює текст у комірці таблиці"""
    changed = False
    for paragraph in cell.paragraphs:
        if replace_text_in_paragraph(paragraph, verbose):
            changed = True
    return changed

def replace_text_in_table(table, verbose=False):
    """Замінює текст у таблиці з урахуванням структури стовпців"""
    changed = False
    for row_idx, row in enumerate(table.rows):
        for cell_idx, cell in enumerate(row.cells):
            if verbose:
                cell_text = ''.join(''.join(run.text for run in p.runs) for p in cell.paragraphs)
                if cell_text.strip():
                    print(f"  📋 Таблиця [рядок {row_idx+1}, стовпець {cell_idx+1}]:")
            # Обробляємо кожну комірку окремо
            if replace_text_in_cell(cell, verbose):
                changed = True
    return changed

def replace_text_in_docx(file_path, verbose=False):
    """Основна функція для заміни тексту в документі Word"""
    try:
        doc = Document(file_path)
        total_changes = 0
        
        if verbose:
            print(f"📄 Обробка документа: {file_path}")

        # Обробка звичайних параграфів
        for i, paragraph in enumerate(doc.paragraphs):
            para_text = ''.join(run.text for run in paragraph.runs)
            if para_text.strip() and verbose:
                print(f"  📝 Параграф {i+1}: {para_text[:50]}...")
            if replace_text_in_paragraph(paragraph, verbose):
                total_changes += 1

        # Обробка таблиць
        for i, table in enumerate(doc.tables):
            if verbose:
                print(f"  📊 Таблиця {i+1}:")
            if replace_text_in_table(table, verbose):
                total_changes += 1

        # Зберігаємо документ
        doc.save(file_path)
        return total_changes
        
    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def main():
    """Головна функція для обробки всіх файлів у папці"""
    print("🔧 Покращений скрипт заміни тексту в Word документах")
    print("=" * 60)
    
    verbose = input("Показувати детальну інформацію? (y/n): ").lower() == 'y'
    
    if not os.path.exists(folder_path):
        print(f'❌ Папка не знайдена: {folder_path}')
        return
    
    processed_files = 0
    total_changes = 0
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.docx') and not filename.startswith('~$'):
            full_path = os.path.join(folder_path, filename)
            print(f'\n🔄 Обробка файлу: {filename}')
            
            changes = replace_text_in_docx(full_path, verbose)
            if changes > 0:
                print(f'✅ Заміна виконана у файлі: {filename} ({changes} змін)')
                total_changes += changes
            else:
                print(f'ℹ️  Змін не знайдено у файлі: {filename}')
            
            processed_files += 1
    
    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print("=" * 60)

if __name__ == "__main__":
    main()
