import os
from docx import Document

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Повні заміни для звичайного тексту
full_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree'
}

# 🔄 Скорочені заміни для таблиць
table_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree'
}

def replace_text_in_paragraph(paragraph, in_table=False):
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False
    
    # Вибираємо відповідний словник замін
    replacements = table_replacements if in_table else full_replacements
    
    for old, new in replacements.items():
        if old in full_text:
            full_text = full_text.replace(old, new)
            changed = True
    
    if changed:
        # Зберігаємо форматування
        original_font = None
        if paragraph.runs:
            font = paragraph.runs[0].font
            original_font = {
                'bold': font.bold,
                'italic': font.italic,
                'size': font.size,
                'name': font.name
            }
        
        # Очищаємо параграф
        paragraph.clear()
        
        # Додаємо новий текст
        run = paragraph.add_run(full_text)
        
        # Відновлюємо форматування
        if original_font:
            if original_font['bold'] is not None:
                run.font.bold = original_font['bold']
            if original_font['italic'] is not None:
                run.font.italic = original_font['italic']
            if original_font['size'] is not None:
                run.font.size = original_font['size']
            if original_font['name'] is not None:
                run.font.name = original_font['name']

def replace_text_in_docx(file_path):
    try:
        doc = Document(file_path)
        changes = 0
        
        print(f"🔄 Обробка файлу: {os.path.basename(file_path)}")
        
        # Обробка звичайних параграфів (повні заміни)
        for paragraph in doc.paragraphs:
            old_text = ''.join(run.text for run in paragraph.runs)
            replace_text_in_paragraph(paragraph, in_table=False)
            new_text = ''.join(run.text for run in paragraph.runs)
            if old_text != new_text:
                changes += 1
        
        # Обробка таблиць (скорочені заміни)
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        old_text = ''.join(run.text for run in paragraph.runs)
                        replace_text_in_paragraph(paragraph, in_table=True)
                        new_text = ''.join(run.text for run in paragraph.runs)
                        if old_text != new_text:
                            changes += 1
        
        # Зберігаємо з новим ім'ям
        output_path = file_path.replace('.docx', '_short.docx')
        doc.save(output_path)
        print(f"✅ Збережено: {os.path.basename(output_path)} ({changes} змін)")
        return changes
        
    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def main():
    print("🔧 Скрипт з скороченими замінами для таблиць")
    print("=" * 60)
    print(f"📁 Папка для обробки: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f'❌ Папка не знайдена: {folder_path}')
        return
    
    docx_files = [f for f in os.listdir(folder_path) if f.endswith('.docx') and not f.startswith('~$') and not f.endswith('_short.docx')]
    if not docx_files:
        print("❌ Не знайдено Word файлів для обробки")
        return
    
    print(f"📄 Знайдено {len(docx_files)} Word файл(ів)")
    print("\n🔄 Початок обробки файлів...")
    print("-" * 60)
    
    total_changes = 0
    processed_files = 0
    
    for filename in docx_files:
        full_path = os.path.join(folder_path, filename)
        changes = replace_text_in_docx(full_path)
        total_changes += changes
        processed_files += 1
    
    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print("=" * 60)

if __name__ == "__main__":
    main()
