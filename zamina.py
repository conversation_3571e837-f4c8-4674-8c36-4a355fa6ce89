import os
from docx import Document
import re

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін для української мови
ukrainian_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Фахівець з геодезії та землеустрою': '',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
}

# 🔄 Список замін для англійської мови
english_replacements = {
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Specialist in geodesy and land management': '',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
}

def is_ukrainian_text(text):
    """Визначає, чи містить текст українські символи"""
    ukrainian_chars = 'абвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ'
    return any(char in ukrainian_chars for char in text)

def is_english_text(text):
    """Визначає, чи містить текст англійські символи"""
    return any(char.isalpha() and char.isascii() for char in text)

def split_mixed_text(text):
    """Розділяє змішаний текст на українську та англійську частини"""
    # Шукаємо патерни типу "2.1 Український текст 2.1 English text"
    pattern = r'(2\.\d+(?:\.\d+)?)\s+([^2]+?)\s+(2\.\d+(?:\.\d+)?)\s+(.+)'
    match = re.search(pattern, text)

    if match:
        ukr_number = match.group(1)
        ukr_text = match.group(2).strip()
        eng_number = match.group(3)
        eng_text = match.group(4).strip()

        # Перевіряємо, що номери співпадають
        if ukr_number == eng_number:
            return ukr_text, eng_text

    # Альтернативний підхід: розділяємо по табуляції
    if '\t' in text:
        parts = text.split('\t')
        if len(parts) >= 2:
            left_part = parts[0].strip()
            right_part = '\t'.join(parts[1:]).strip()

            # Визначаємо мову кожної частини
            if is_ukrainian_text(left_part) and is_english_text(right_part):
                return left_part, right_part
            elif is_english_text(left_part) and is_ukrainian_text(right_part):
                return right_part, left_part

    return None, None

def apply_replacements(text, language):
    """Застосовує заміни до тексту залежно від мови"""
    if language == 'ukrainian':
        replacements = ukrainian_replacements
    elif language == 'english':
        replacements = english_replacements
    else:
        replacements = {**ukrainian_replacements, **english_replacements}

    changed = False
    for old, new in replacements.items():
        if old in text:
            text = text.replace(old, new)
            changed = True

    return text, changed



def replace_text_in_paragraph(paragraph):
    """Обробляє параграф, розподіляючи текст і виконуючи заміни"""
    full_text = ''.join(run.text for run in paragraph.runs)

    # Спочатку пробуємо розділити змішаний текст
    ukrainian_text, english_text = split_mixed_text(full_text)

    if ukrainian_text and english_text:
        print(f"  📋 Знайдено змішаний текст")

        # Застосовуємо заміни до кожної частини окремо
        ukrainian_text, ukr_changed = apply_replacements(ukrainian_text, 'ukrainian')
        english_text, eng_changed = apply_replacements(english_text, 'english')

        if ukr_changed or eng_changed:
            # Зберігаємо форматування
            original_font = None
            if paragraph.runs:
                font = paragraph.runs[0].font
                original_font = {
                    'bold': font.bold,
                    'italic': font.italic,
                    'size': font.size,
                    'name': font.name
                }

            # Очищаємо оригінальний параграф
            paragraph.clear()

            # Додаємо український текст
            ukr_run = paragraph.add_run(ukrainian_text)

            # Додаємо табуляцію для розділення
            paragraph.add_run('\t')

            # Додаємо англійський текст
            eng_run = paragraph.add_run(english_text)

            # Відновлюємо форматування
            if original_font:
                for run in [ukr_run, eng_run]:
                    if original_font['bold'] is not None:
                        run.font.bold = original_font['bold']
                    if original_font['italic'] is not None:
                        run.font.italic = original_font['italic']
                    if original_font['size'] is not None:
                        run.font.size = original_font['size']
                    if original_font['name'] is not None:
                        run.font.name = original_font['name']

            print(f"    ✅ Застосовано заміни до змішаного тексту")
            return True
    else:
        # Обробляємо як звичайний текст
        if is_ukrainian_text(full_text):
            language = 'ukrainian'
        elif is_english_text(full_text):
            language = 'english'
        else:
            language = 'mixed'

        new_text, changed = apply_replacements(full_text, language)

        if changed:
            # Зберігаємо форматування
            original_font = None
            if paragraph.runs:
                font = paragraph.runs[0].font
                original_font = {
                    'bold': font.bold,
                    'italic': font.italic,
                    'size': font.size,
                    'name': font.name
                }

            # Очищаємо параграф і додаємо новий текст
            paragraph.clear()
            run = paragraph.add_run(new_text)

            # Відновлюємо форматування
            if original_font:
                if original_font['bold'] is not None:
                    run.font.bold = original_font['bold']
                if original_font['italic'] is not None:
                    run.font.italic = original_font['italic']
                if original_font['size'] is not None:
                    run.font.size = original_font['size']
                if original_font['name'] is not None:
                    run.font.name = original_font['name']

            print(f"    ✅ Застосовано заміни до {language} тексту")
            return True

    return False

def replace_text_in_docx(file_path):
    try:
        doc = Document(file_path)
        changes = 0

        print(f"🔄 Обробка файлу: {os.path.basename(file_path)}")

        # Обробка звичайних параграфів
        print("  📝 Обробка параграфів...")
        paragraphs_to_process = list(doc.paragraphs)  # Копія для безпечної ітерації
        for paragraph in paragraphs_to_process:
            if replace_text_in_paragraph(paragraph):
                changes += 1

        # Обробка таблиць
        print("  📋 Обробка таблиць...")
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if replace_text_in_paragraph(paragraph):
                            changes += 1

        # Зберігаємо з новим ім'ям
        output_path = file_path.replace('.docx', '_split.docx')
        doc.save(output_path)
        print(f"✅ Збережено: {os.path.basename(output_path)} ({changes} змін)")
        return changes

    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def main():
    print("🔧 Скрипт розділення змішаного тексту з табуляцією")
    print("=" * 60)
    print(f"📁 Папка для обробки: {folder_path}")

    if not os.path.exists(folder_path):
        print(f'❌ Папка не знайдена: {folder_path}')
        return

    docx_files = [f for f in os.listdir(folder_path) if f.endswith('.docx') and not f.startswith('~$') and not f.endswith('_split.docx')]
    if not docx_files:
        print("❌ Не знайдено Word файлів для обробки")
        return

    print(f"📄 Знайдено {len(docx_files)} Word файл(ів)")
    print("\n🔄 Початок обробки файлів...")
    print("-" * 60)

    total_changes = 0
    processed_files = 0

    for filename in docx_files:
        full_path = os.path.join(folder_path, filename)
        changes = replace_text_in_docx(full_path)
        total_changes += changes
        processed_files += 1

    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print(f'   Файли збережені з суфіксом "_split.docx"')
    print("=" * 60)

if __name__ == "__main__":
    main()
