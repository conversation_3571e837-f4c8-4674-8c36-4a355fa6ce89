import os
from docx import Document

# 🔷 Шлях до папки з Word-файлами (буде запитано у користувача)
folder_path = None

# 🔄 Список замін для української мови
ukrainian_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Фахівець з геодезії та землеустрою': '',
    'Основна	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': 'Мова(и) навчання/оцінювання',
}

# 🔄 Список замін для англійської мови
english_replacements = {
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and  educational-professional degree conferred (in original language)',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Specialist in geodesy and land management': '',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Name and status of institution (if different from 2.3) administering studies': 'Language(s) of instruction/examination',
}

# Текст для видалення
text_to_remove = [
    'Зазначено у пункті 2.3\tSpecified in 2.3',
    '2.5\tМова(и) навчання/оцінювання\t2.5 Language(s) of instruction/examination'
]

def is_ukrainian_text(text):
    """Визначає, чи містить текст українські символи"""
    ukrainian_chars = 'абвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ'
    return any(char in ukrainian_chars for char in text)

def is_english_text(text):
    """Визначає, чи містить текст англійські символи"""
    return any(char.isalpha() and char.isascii() for char in text)

def replace_text_in_run_preserving_format(run, old_text, new_text):
    """Замінює текст у run зі збереженням форматування"""
    if old_text in run.text:
        # Зберігаємо форматування
        font = run.font
        original_bold = font.bold
        original_italic = font.italic
        original_underline = font.underline
        original_size = font.size
        original_color = font.color.rgb if font.color.rgb else None

        # Замінюємо текст
        run.text = run.text.replace(old_text, new_text)

        # Відновлюємо форматування
        font.bold = original_bold
        font.italic = original_italic
        font.underline = original_underline
        if original_size:
            font.size = original_size
        if original_color:
            font.color.rgb = original_color

        return True
    return False

def replace_text_in_paragraph(paragraph):
    """Замінює текст у параграфі зі збереженням форматування"""
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False

    # Видалення вказаних рядків
    for remove_text in text_to_remove:
        if remove_text in full_text:
            for run in paragraph.runs:
                if remove_text in run.text:
                    run.text = run.text.replace(remove_text, '')
                    changed = True

    # Визначаємо мову тексту та застосовуємо відповідні заміни
    if is_ukrainian_text(full_text):
        replacements = ukrainian_replacements
    elif is_english_text(full_text):
        replacements = english_replacements
    else:
        # Якщо мова не визначена, використовуємо обидва словники
        replacements = {**ukrainian_replacements, **english_replacements}

    # Застосування замін
    for old, new in replacements.items():
        for run in paragraph.runs:
            if replace_text_in_run_preserving_format(run, old, new):
                changed = True

    return changed

def replace_text_in_cell(cell):
    """Замінює текст у комірці таблиці"""
    changed = False
    for paragraph in cell.paragraphs:
        if replace_text_in_paragraph(paragraph):
            changed = True
    return changed

def replace_text_in_table(table):
    """Замінює текст у таблиці з урахуванням структури стовпців"""
    changed = False
    for row_idx, row in enumerate(table.rows):
        for cell_idx, cell in enumerate(row.cells):
            # Обробляємо кожну комірку окремо
            if replace_text_in_cell(cell):
                changed = True
    return changed

def replace_text_in_docx(file_path):
    """Основна функція для заміни тексту в документі Word"""
    try:
        doc = Document(file_path)
        total_changes = 0

        # Обробка звичайних параграфів
        for paragraph in doc.paragraphs:
            if replace_text_in_paragraph(paragraph):
                total_changes += 1

        # Обробка таблиць
        for table in doc.tables:
            if replace_text_in_table(table):
                total_changes += 1

        # Зберігаємо документ
        doc.save(file_path)
        return total_changes

    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def get_folder_path():
    """Запитує у користувача шлях до папки з Word файлами"""
    print("🔧 Скрипт заміни тексту в Word документах")
    print("=" * 50)

    while True:
        folder_path = input("📁 Введіть шлях до папки з Word файлами: ").strip()

        # Видаляємо лапки, якщо користувач їх додав
        if folder_path.startswith('"') and folder_path.endswith('"'):
            folder_path = folder_path[1:-1]
        if folder_path.startswith("'") and folder_path.endswith("'"):
            folder_path = folder_path[1:-1]

        if not folder_path:
            print("❌ Шлях не може бути порожнім!")
            continue

        if not os.path.exists(folder_path):
            print(f"❌ Папка не знайдена: {folder_path}")
            retry = input("Спробувати ще раз? (y/n): ").lower()
            if retry != 'y':
                return None
            continue

        if not os.path.isdir(folder_path):
            print(f"❌ Це не папка: {folder_path}")
            continue

        # Перевіряємо, чи є в папці Word файли
        docx_files = [f for f in os.listdir(folder_path) if f.endswith('.docx') and not f.startswith('~$')]
        if not docx_files:
            print(f"⚠️  У папці {folder_path} не знайдено Word файлів (.docx)")
            proceed = input("Продовжити все одно? (y/n): ").lower()
            if proceed != 'y':
                continue
        else:
            print(f"✅ Знайдено {len(docx_files)} Word файл(ів)")

        return folder_path

def main():
    """Головна функція для обробки всіх файлів у папці"""
    folder_path = get_folder_path()

    if not folder_path:
        print("❌ Операція скасована")
        return

    processed_files = 0
    total_changes = 0

    print(f"\n🔄 Початок обробки файлів у папці: {folder_path}")
    print("-" * 50)

    for filename in os.listdir(folder_path):
        if filename.endswith('.docx') and not filename.startswith('~$'):
            full_path = os.path.join(folder_path, filename)
            print(f'🔄 Обробка файлу: {filename}')

            changes = replace_text_in_docx(full_path)
            if changes > 0:
                print(f'✅ Заміна виконана у файлі: {filename} ({changes} змін)')
                total_changes += changes
            else:
                print(f'ℹ️  Змін не знайдено у файлі: {filename}')

            processed_files += 1

    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print("=" * 50)

if __name__ == "__main__":
    main()
