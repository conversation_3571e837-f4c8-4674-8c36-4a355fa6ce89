import os
from docx import Document
from docx.enum.section import WD_SECTION
from docx.shared import Inches
import re

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін для української мови
ukrainian_replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Фахівець з геодезії та землеустрою': '',
    'Основна	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
}

# 🔄 Список замін для англійської мови
english_replacements = {
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Specialist in geodesy and land management': '',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
}

def is_ukrainian_text(text):
    """Визначає, чи містить текст українські символи"""
    ukrainian_chars = 'абвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ'
    return any(char in ukrainian_chars for char in text)

def is_english_text(text):
    """Визначає, чи містить текст англійські символи"""
    return any(char.isalpha() and char.isascii() for char in text)

def split_mixed_text(text):
    """Розділяє змішаний текст на українську та англійську частини"""
    # Шукаємо патерни типу "2.1 Український текст 2.1 English text"
    pattern = r'(2\.\d+(?:\.\d+)?)\s+([^2]+?)\s+(2\.\d+(?:\.\d+)?)\s+(.+)'
    match = re.search(pattern, text)

    if match:
        ukr_number = match.group(1)
        ukr_text = match.group(2).strip()
        eng_number = match.group(3)
        eng_text = match.group(4).strip()

        # Перевіряємо, що номери співпадають
        if ukr_number == eng_number:
            return ukr_text, eng_text

    # Альтернативний підхід: розділяємо по табуляції
    if '\t' in text:
        parts = text.split('\t')
        if len(parts) >= 2:
            left_part = parts[0].strip()
            right_part = '\t'.join(parts[1:]).strip()

            # Визначаємо мову кожної частини
            if is_ukrainian_text(left_part) and is_english_text(right_part):
                return left_part, right_part
            elif is_english_text(left_part) and is_ukrainian_text(right_part):
                return right_part, left_part

    return None, None

def apply_replacements(text, language):
    """Застосовує заміни до тексту залежно від мови"""
    if language == 'ukrainian':
        replacements = ukrainian_replacements
    elif language == 'english':
        replacements = english_replacements
    else:
        replacements = {**ukrainian_replacements, **english_replacements}

    changed = False
    for old, new in replacements.items():
        if old in text:
            text = text.replace(old, new)
            changed = True

    return text, changed

def create_two_column_paragraph(doc, ukr_text, eng_text):
    """Створює параграф з двома стовпчиками"""
    # Створюємо таблицю з 1 рядком і 2 стовпчиками
    table = doc.add_table(rows=1, cols=2)
    table.style = 'Table Grid'

    # Налаштовуємо ширину стовпців
    table.columns[0].width = Inches(3.5)  # Український стовпець
    table.columns[1].width = Inches(3.5)  # Англійський стовпець

    # Додаємо текст до стовпців
    ukr_cell = table.cell(0, 0)
    eng_cell = table.cell(0, 1)

    ukr_cell.text = ukr_text
    eng_cell.text = eng_text

    # Прибираємо рамки таблиці для вигляду стовпців
    for row in table.rows:
        for cell in row.cells:
            cell._element.get_or_add_tcPr().append(
                cell._element.xpath('.//w:tcBorders')[0] if cell._element.xpath('.//w:tcBorders') else
                cell._element.get_or_add_tcPr().append(cell._element._new_child('w:tcBorders'))
            )

def replace_text_in_paragraph(paragraph, doc):
    """Обробляє параграф, розподіляючи текст по стовпчиках і виконуючи заміни"""
    full_text = ''.join(run.text for run in paragraph.runs)

    # Спочатку пробуємо розділити змішаний текст
    ukrainian_text, english_text = split_mixed_text(full_text)

    if ukrainian_text and english_text:
        # Застосовуємо заміни до кожної частини окремо
        ukrainian_text, ukr_changed = apply_replacements(ukrainian_text, 'ukrainian')
        english_text, eng_changed = apply_replacements(english_text, 'english')

        if ukr_changed or eng_changed:
            # Очищаємо оригінальний параграф
            paragraph.clear()

            # Створюємо двоколонковий текст
            paragraph.add_run(ukrainian_text + '\t' + english_text)
            return True
    else:
        # Обробляємо як звичайний текст
        if is_ukrainian_text(full_text):
            language = 'ukrainian'
        elif is_english_text(full_text):
            language = 'english'
        else:
            language = 'mixed'

        new_text, changed = apply_replacements(full_text, language)

        if changed:
            # Очищаємо параграф і додаємо новий текст
            paragraph.clear()
            paragraph.add_run(new_text)
            return True

    return False

def set_two_column_layout(doc):
    """Встановлює двоколонкове форматування для документа"""
    try:
        for section in doc.sections:
            # Встановлюємо двоколонкове форматування
            sectPr = section._sectPr
            cols = sectPr.xpath('./w:cols')
            if cols:
                cols[0].set('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}num', '2')
            else:
                from docx.oxml import OxmlElement
                cols_element = OxmlElement('w:cols')
                cols_element.set('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}num', '2')
                cols_element.set('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}space', '708')  # 0.5 inch spacing
                sectPr.append(cols_element)
    except Exception as e:
        print(f"  ⚠️  Не вдалося встановити двоколонкове форматування: {e}")

def replace_text_in_docx(file_path):
    try:
        doc = Document(file_path)
        changes = 0

        print(f"🔄 Обробка файлу: {os.path.basename(file_path)}")

        # Обробка звичайних параграфів
        paragraphs_to_process = list(doc.paragraphs)  # Копія для безпечної ітерації
        for paragraph in paragraphs_to_process:
            if replace_text_in_paragraph(paragraph, doc):
                changes += 1

        # Обробка таблиць
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if replace_text_in_paragraph(paragraph, doc):
                            changes += 1

        # Встановлюємо двоколонкове форматування якщо були зміни
        if changes > 0:
            set_two_column_layout(doc)
            print(f"  🔧 Встановлено двоколонкове форматування")

        # Зберігаємо з новим ім'ям
        output_path = file_path.replace('.docx', '_columns.docx')
        doc.save(output_path)
        print(f"✅ Збережено: {os.path.basename(output_path)} ({changes} змін)")
        return changes

    except Exception as e:
        print(f'❌ Помилка при обробці файлу {file_path}: {str(e)}')
        return 0

def main():
    print("🔧 Скрипт створення двоколонкового форматування")
    print("=" * 60)
    print(f"📁 Папка для обробки: {folder_path}")

    if not os.path.exists(folder_path):
        print(f'❌ Папка не знайдена: {folder_path}')
        return

    docx_files = [f for f in os.listdir(folder_path) if f.endswith('.docx') and not f.startswith('~$') and not f.endswith('_columns.docx')]
    if not docx_files:
        print("❌ Не знайдено Word файлів для обробки")
        return

    print(f"📄 Знайдено {len(docx_files)} Word файл(ів)")
    print("\n🔄 Початок обробки файлів...")
    print("-" * 60)

    total_changes = 0
    processed_files = 0

    for filename in docx_files:
        full_path = os.path.join(folder_path, filename)
        changes = replace_text_in_docx(full_path)
        total_changes += changes
        processed_files += 1

    print(f'\n📊 Підсумок:')
    print(f'   Оброблено файлів: {processed_files}')
    print(f'   Загальна кількість змін: {total_changes}')
    print("=" * 60)

if __name__ == "__main__":
    main()
