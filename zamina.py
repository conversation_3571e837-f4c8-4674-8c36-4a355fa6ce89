
import os
from docx import Document

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін
replacements = {
    # Англійські заміни
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and  educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    # tyt
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    #tyt
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': 'Мова(и) навчання/оцінювання',
    'Name and status of institution (if different from 2.3) administering studies': 'Language(s) of instruction/examination',
    }

def replace_text_in_paragraph(paragraph):
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False
    for old, new in replacements.items():
        if old in full_text:
            full_text = full_text.replace(old, new)
            changed = True
    if changed:
        # Очистити всі run'и і вставити новий текст в один run
        for run in paragraph.runs:
            run.text = ''
        paragraph.runs[0].text = full_text  # вставляємо весь текст у перший run

def replace_text_in_cell(cell):
    for paragraph in cell.paragraphs:
        replace_text_in_paragraph(paragraph)

def replace_text_in_docx(file_path):
    doc = Document(file_path)
    for paragraph in doc.paragraphs:
        replace_text_in_paragraph(paragraph)
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_cell(cell)
    doc.save(file_path)

for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        replace_text_in_docx(full_path)
        print(f'✅ Заміна виконана у файлі: {filename}')
