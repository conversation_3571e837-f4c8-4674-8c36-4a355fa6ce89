import os
from docx import Document
from docx.oxml.ns import qn
from docx.text.run import Run
from docx.enum.section import WD_SECTION
from docx.oxml import OxmlElement
from docx.shared import Pt
import re

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree'
}

def replace_text_in_run(run, old_text, new_text, filename):
    """Замінює текст у run, зберігаючи форматування, і зменшує шрифт для довгих замін."""
    if old_text in run.text:
        if len(new_text) > len(old_text) * 1.5:
            print(f"Попередження: Заміна '{old_text}' на '{new_text}' у файлі {filename} може спричинити зміщення тексту.")
            run.font.size = Pt(10)  # Зменшення шрифту до 10 pt
        run.text = run.text.replace(old_text, new_text)

def split_text_into_columns(paragraph_text):
    """Розділяє текст на українську та англійську частини за наявності ключових слів."""
    # Спрощене розпізнавання: розділяємо за першим входженням англійського тексту
    match = re.search(r'(2\.\d+)\s+(.+?)\s+(2\.\d+\s+.+)', paragraph_text)
    if match:
        ukrainian_part = match.group(2).strip()
        english_part = match.group(3).strip()
        return ukrainian_part, english_part
    return None, None

def process_paragraph(paragraph, doc, filename):
    """Обробляє параграф, розподіляючи текст по стовпчиках і виконуючи заміни."""
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False

    # Перевіряємо, чи параграф містить текст для заміни
    for old, new in replacements.items():
        if old in full_text:
            for run in paragraph.runs:
                replace_text_in_run(run, old, new, filename)
            changed = True

    # Розділяємо текст на стовпчики, якщо це потрібно
    ukrainian_text, english_text = split_text_into_columns(full_text)
    if ukrainian_text and english_text and changed:
        # Створюємо новий параграф для англійського тексту
        new_paragraph = doc.add_paragraph()
        new_paragraph.paragraph_format.alignment = paragraph.paragraph_format.alignment

        # Очищаємо оригінальний параграф і вставляємо український текст
        for run in paragraph.runs:
            run.text = ''
        paragraph.runs[0].text = ukrainian_text

        # Вставляємо англійський текст у новий параграф
        new_paragraph.runs[0].text = english_text

def process_cell(cell, doc, filename):
    """Обробляє клітинки таблиць."""
    for paragraph in cell.paragraphs:
        process_paragraph(paragraph, doc, filename)

def ensure_two_columns(doc):
    """Встановлює двоколонкове форматування для всіх секцій."""
    for section in doc.sections:
        if section._sectPr is None:
            sectPr = OxmlElement('w:sectPr')
            section._element.append(sectPr)
        else:
            sectPr = section._sectPr

        cols = sectPr.xpath('./w:cols')[0] if sectPr.xpath('./w:cols') else None
        if cols is None:
            cols = OxmlElement('w:cols')
            cols.set(qn('w:num'), '2')  # Встановлюємо 2 стовпчики
            sectPr.append(cols)
        else:
            num_cols = cols.get(qn('w:num'), '1')
            if num_cols != '2':
                cols.set(qn('w:num'), '2')  # Примусово встановлюємо 2 стовпчики

def process_docx(file_path, output_path):
    """Обробляє документ, розподіляючи текст по стовпчиках і виконуючи заміни."""
    doc = Document(file_path)

    # Встановлюємо двоколонкове форматування
    ensure_two_columns(doc)

    # Обробка параграфів
    paragraphs_to_process = list(doc.paragraphs)  # Копія для ітерації
    for paragraph in paragraphs_to_process:
        process_paragraph(paragraph, doc, os.path.basename(file_path))

    # Обробка таблиць
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                process_cell(cell, doc, os.path.basename(file_path))

    # Збереження результату
    doc.save(output_path)

# Обробка всіх файлів у папці
for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        input_path = os.path.join(folder_path, filename)
        output_path = os.path.join(folder_path, f"formatted_{filename}")
        process_docx(input_path, output_path)
        print(f'✅ Обробка виконана для файлу: {filename}, результат збережений як {os.path.basename(output_path)}')
