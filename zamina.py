import os
from docx import Document

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін
replacements = {
    # Англійські заміни
      'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and  educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    # tyt
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна	(основні)	галузь	(галузі) знань	за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    #tyt
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': 'Мова(и) навчання/оцінювання',
    'Name and status of institution (if different from 2.3) administering studies': 'Language(s) of instruction/examination',
    }

# Текст для видалення
text_to_remove = [
    'Зазначено у пункті 2.3\tSpecified in 2.3',
    '2.5\tМова(и) навчання/оцінювання\t2.5 Language(s) of instruction/examination'
]

def replace_text_in_paragraph(paragraph):
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False

    # Видалення вказаної строки
    for remove_text in text_to_remove:
        if remove_text in full_text:
            full_text = full_text.replace(remove_text, '')
            changed = True

    # Застосування заміни
    for old, new in replacements.items():
        if old in full_text:
            lines = new.split('\n')
            if len(lines) > 1:
                first_line = lines[0]
                rest_lines = '\n'.join(lines[1:])
                full_text = full_text.replace(old, first_line)
                changed = True
            else:
                full_text = full_text.replace(old, new)
                changed = True

    if changed:
        # Очистити всі run'и
        for run in paragraph.runs:
            run.text = ''
        # Вставляємо оновлений текст у перший run
        paragraph.runs[0].text = full_text
        # Якщо є додаткові рядки, додаємо новий абзац
        if len(lines) > 1 and rest_lines:
            new_paragraph = paragraph.insert_paragraph_before(rest_lines)
            new_paragraph.runs[0].text = rest_lines

def replace_text_in_cell(cell):
    for paragraph in cell.paragraphs:
        replace_text_in_paragraph(paragraph)

def replace_text_in_docx(file_path):
    doc = Document(file_path)
    for paragraph in doc.paragraphs:
        replace_text_in_paragraph(paragraph)
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_cell(cell)
    doc.save(file_path)

for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        replace_text_in_docx(full_path)
        print(f'✅ Заміна виконана у файлі: {filename}')
